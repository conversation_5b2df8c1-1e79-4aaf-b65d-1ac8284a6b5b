#pragma once

#include <QTextDocument>
#include <QTextEdit>

#include <QApplication>
#include <QBoxLayout>
#include <QCborMap>
#include <QDialog>
#include <QFile>
#include <QGroupBox>
#include <QLabel>
#include <QMutex>
#include <QProgressBar>
#include <QSplitter>
#include <QString>
#include <QStringLiteral>
#include <QTableView>
#include <QTextDocumentFragment>
#include <QThread>
#include <QTreeView>
using namespace Qt::Literals::StringLiterals;

#include "CodeEditor.h"
#include "InternalView.h"
#include "OutputWidget.h"
#include "SearchDialog.h"
#include "OutputData.h"
#include "HelpWidget.h"

class QPanelWidget : public QGroupBox
{
    Q_OBJECT
public:
    explicit QPanelWidget( QString sLabel, QWidget* content, QWidget* parent )
        : QGroupBox( u"  "_s + sLabel, parent )
    {
        auto layOutput = new QVBoxLayout;
        layOutput->addWidget( content );
        setLayout( layOutput );
    }
};

// This widget is each open document
class QContentEngine;

class QDocumentWidget : public QSplitter
{
    Q_OBJECT
public:
    explicit QDocumentWidget( const QString sFileName, const QByteArray& baContents, const bool bReadOnly, QWidget* parent );
    ~QDocumentWidget();

    QString fileName() const;

    void syncRunActionsWithEngine();

    static constexpr int HELP_HANDLE = -1;

public:
    QString        m_fileName;
    enumCodeType   m_codeType{ enumCodeType::codeTypeMarkdown };
    QCodeEditor    m_inputEditor;
    QInternalsView m_internalsView;
    QOutputWidget  m_outputView;
    QOutputData    m_output;
    QHelpWidget    m_helpWidget;
    bool           m_bReadOnly{ true };

    QCborMap                           m_mapMemory;
    QSearchDialog::SearchContext       m_contextSearch;

    QContentEngine* m_engine{ nullptr };

    void setContentEngine( QContentEngine* engine );
    bool canClose();

protected:
    void readFileContents( const enumFileFormat fileFormat, const QByteArray& baFileContents );
    void readFileContentsJupyter( const QByteArray& baFileContents );
    void readFileContentsPython( const QByteArray& baFileContents );
    void readFileContentsMarkdown( const QByteArray& baFileContents );
    void readFileContentsPlain( const QByteArray& baFileContents );

    QByteArray writeFileContents( const enumFileFormat fileFormat );
    QByteArray writeFileContentsJupyter();
    QByteArray writeFileContentsPython();
    QByteArray writeFileContentsPlain();
    void appendMarkdown( QJsonArray& jsonCells, const int lineStart, const int lineEnd );
    void appendCode( QJsonArray& jsonCells, const int lineStart, const int lineEnd );

    bool isFocal() const;

public Q_SLOTS:
    void onFileSave();
    void onFileSaveAs();
    void onFilePrint();
    void onFilePdf();
    void onEditUndo();
    void onEditRedo();
    void onViewToggleMemoryVisible();

    void onEditFind();
    void onEditFindNext();
    void onEditFindPrevious();
    void onSearch( const QSearchDialog::SearchContext& contextSearch, const QSearchDialog::enumSearchAction actionSearch );

    void onCodeSetup();
    void onCodeRunStart();
    void onCodeRunStop();
    void onCodeRunKill();
    void onCodeRunRestart();
    void onCodeRunRestartRunAll();

    void onProgressJobStart();
    void onProgressLineStart( const int executionHandle, const QString& sCode, const int executionSequence );
    void onProgressLineAnnotation( const int executionHandle, const QString& sAnnotation );
    void onProgressLineText( const int executionHandle, const QString& sText, const enumContentFormat contentFormat );
    void onProgressLineError( const int executionHandle, const QString& sSummary, const QString& sDetail );
    void onProgressLineSvg( const int executionHandle, const QByteArray& baSvg );
    void onProgressLineImage( const int executionHandle, const QImage& img );
    void onProgressLineEnd( const int executionHandle, const bool bError );
    void onProgressJobEnd( const bool bSuccess );
    void onProgressRestartBegin();
    void onProgressRestartEnd();

    void onRequestHelp( const QString& sSearch );
    void onEditorReportedHelp(const QString& sContents );

    void saveFile( const QString& fileName );
    void saveFileData( const QString& fileName, const QByteArray& baData );
Q_SIGNALS:
    void requestSetup( const QMap< QString, QString >& mapSetup );
    void requestExecute( const QList< int >& chunkHandles, const QList< QString >& chunkCodes );
    void requestRestart();
    void requestStop();
    void requestKill();
    void helpAvailable( const QString& sContents );

};
