cmake_minimum_required(VERSION 3.16)

set(CMAKE_OSX_ARCHITECTURES "x86_64;arm64" CACHE STRING "" FORCE)

set(CMAKE_OSX_DEPLOYMENT_TARGET "12" CACHE STRING "" FORCE)

project(TrySail VERSION 0.1.11 LANGUAGES C CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

if(DEFINED LINK_NUITKA_EMBED)
    set(internal_LINK_NUITKA_EMBED ${LINK_NUITKA_EMBED})
else()
    set(internal_LINK_NUITKA_EMBED OFF)
endif()

if(LINUX)
    set(CMAKE_POSITION_INDEPENDENT_CODE ON CACHE BOOL "Default value for POSITION_INDEPENDENT_CODE")
endif()

find_package(Qt6 REQUIRED COMPONENTS Core Gui Network PrintSupport Sql Svg Widgets)

qt_standard_project_setup()

file(GLOB SCINTILLA_SOURCES scintilla/src/*.cxx scintilla/qt/ScintillaEditBase/*.cpp scintilla/qt/ScintillaEditBase/*.h scintilla/qt/ScintillaEdit/*.cpp scintilla/qt/ScintillaEdit/*.h)
file(GLOB LEXILLA_SOURCES lexilla/lexers/*.cxx lexilla/lexlib/*.cxx lexilla/src/*.cxx)

if (APPLE)
    set(MACOSX_BUNDLE_ICON_FILE TrySail.icns)
    set(TRYSAIL_ICON ${CMAKE_SOURCE_DIR}/Resources/${MACOSX_BUNDLE_ICON_FILE})
    set_source_files_properties(${TRYSAIL_ICON} PROPERTIES
        MACOSX_PACKAGE_LOCATION "Resources")
endif()

qt_add_big_resources(TRYSAIL_RESOURCES TrySail.qrc)

qt_add_executable(TrySail WIN32 MACOSX_BUNDLE
    ${TRYSAIL_ICON}
    AboutDialog.cpp AboutDialog.h
    App.cpp App.h

    CodeEditor.cpp CodeEditor.h
    ConfigDialog.cpp ConfigDialog.h
    ContentEngine.cpp ContentEngine.h
    PythonEngine.cpp PythonEngine.h
    SqlEngine.cpp SqlEngine.h
    PythonThread.cpp PythonThread.h

    DocumentWidget.cpp DocumentWidget.h
    FileExplorerWidget.cpp FileExplorerWidget.h
    GithubDialog.cpp GithubDialog.h
    HelpWidget.cpp HelpWidget.h
    InternalView.cpp InternalView.h
    MainWindow.cpp MainWindow.h
    OutputData.cpp OutputData.h
    OutputWidget.cpp OutputWidget.h
    SearchDialog.cpp SearchDialog.h
    TourWidget.cpp TourWidget.h
    TrySailGlobal.cpp TrySailGlobal.h
    SvgTextObject.cpp SvgTextObject.h
    qxtflowview.h qxtflowview.cpp
    ${SCINTILLA_SOURCES}
    ${LEXILLA_SOURCES}
    ${TRYSAIL_RESOURCES}


    # LSP Messages
    lsp/messages/LspMessages.h
    lsp/messages/base/LspMessage.h
    lsp/messages/base/LspRequest.cpp lsp/messages/base/LspRequest.h
    lsp/messages/base/LspResponse.cpp lsp/messages/base/LspResponse.h
    lsp/messages/base/LspNotification.cpp lsp/messages/base/LspNotification.h
    lsp/messages/base/LspError.cpp lsp/messages/base/LspError.h
    lsp/messages/base/GenericMessages.h
    lsp/messages/lifecycle/InitializeRequest.cpp lsp/messages/lifecycle/InitializeRequest.h
    lsp/messages/lifecycle/InitializeResponse.cpp lsp/messages/lifecycle/InitializeResponse.h
    lsp/messages/lifecycle/InitializedNotification.cpp lsp/messages/lifecycle/InitializedNotification.h
    lsp/messages/lifecycle/ShutdownRequest.cpp lsp/messages/lifecycle/ShutdownRequest.h
    lsp/messages/lifecycle/ShutdownResponse.cpp lsp/messages/lifecycle/ShutdownResponse.h
    lsp/messages/lifecycle/ExitNotification.cpp lsp/messages/lifecycle/ExitNotification.h
    lsp/messages/completion/CompletionRequest.cpp lsp/messages/completion/CompletionRequest.h
    lsp/messages/completion/CompletionResponse.cpp lsp/messages/completion/CompletionResponse.h
    lsp/messages/document/DidOpenNotification.cpp lsp/messages/document/DidOpenNotification.h
    lsp/messages/document/DidChangeNotification.cpp lsp/messages/document/DidChangeNotification.h
    lsp/messages/document/DidSaveNotification.cpp lsp/messages/document/DidSaveNotification.h
    lsp/messages/document/DidCloseNotification.cpp lsp/messages/document/DidCloseNotification.h
    lsp/messages/document/WillSaveNotification.cpp lsp/messages/document/WillSaveNotification.h
    lsp/messages/document/WillSaveWaitUntilRequest.cpp lsp/messages/document/WillSaveWaitUntilRequest.h
    lsp/messages/document/WillSaveWaitUntilResponse.cpp lsp/messages/document/WillSaveWaitUntilResponse.h

    # Language feature messages
    lsp/messages/language/HoverRequest.cpp lsp/messages/language/HoverRequest.h
    lsp/messages/language/HoverResponse.cpp lsp/messages/language/HoverResponse.h
    lsp/messages/language/DefinitionRequest.cpp lsp/messages/language/DefinitionRequest.h
    lsp/messages/language/DefinitionResponse.cpp lsp/messages/language/DefinitionResponse.h
    lsp/messages/language/DeclarationRequest.cpp lsp/messages/language/DeclarationRequest.h
    lsp/messages/language/DeclarationResponse.cpp lsp/messages/language/DeclarationResponse.h
    lsp/messages/language/TypeDefinitionRequest.cpp lsp/messages/language/TypeDefinitionRequest.h
    lsp/messages/language/TypeDefinitionResponse.cpp lsp/messages/language/TypeDefinitionResponse.h
    lsp/messages/language/ImplementationRequest.cpp lsp/messages/language/ImplementationRequest.h
    lsp/messages/language/ImplementationResponse.cpp lsp/messages/language/ImplementationResponse.h
    lsp/messages/language/DocumentSymbolRequest.cpp lsp/messages/language/DocumentSymbolRequest.h
    lsp/messages/language/DocumentHighlightRequest.cpp lsp/messages/language/DocumentHighlightRequest.h
    lsp/messages/language/DocumentHighlightResponse.cpp lsp/messages/language/DocumentHighlightResponse.h
    lsp/messages/language/ReferencesRequest.cpp lsp/messages/language/ReferencesRequest.h
    lsp/messages/language/ReferencesResponse.cpp lsp/messages/language/ReferencesResponse.h
    lsp/messages/language/RenameRequest.cpp lsp/messages/language/RenameRequest.h
    lsp/messages/language/RenameResponse.cpp lsp/messages/language/RenameResponse.h
    lsp/messages/language/CodeActionRequest.cpp lsp/messages/language/CodeActionRequest.h
    lsp/messages/language/CodeActionResponse.cpp lsp/messages/language/CodeActionResponse.h
    lsp/messages/language/SignatureHelpRequest.cpp lsp/messages/language/SignatureHelpRequest.h
    lsp/messages/language/SignatureHelpResponse.cpp lsp/messages/language/SignatureHelpResponse.h
    lsp/messages/language/SemanticTokensRequest.cpp lsp/messages/language/SemanticTokensRequest.h
    lsp/messages/language/SemanticTokensResponse.cpp lsp/messages/language/SemanticTokensResponse.h
    lsp/messages/language/CompletionItemResolveRequest.cpp lsp/messages/language/CompletionItemResolveRequest.h
    lsp/messages/language/CompletionItemResolveResponse.cpp lsp/messages/language/CompletionItemResolveResponse.h
    lsp/messages/language/PublishDiagnosticsNotification.cpp lsp/messages/language/PublishDiagnosticsNotification.h

    # Workspace messages
    lsp/messages/workspace/WorkspaceSymbolRequest.cpp lsp/messages/workspace/WorkspaceSymbolRequest.h
    lsp/messages/workspace/WorkspaceSymbolResponse.cpp lsp/messages/workspace/WorkspaceSymbolResponse.h
    lsp/messages/workspace/ConfigurationRequest.cpp lsp/messages/workspace/ConfigurationRequest.h

    # Window messages
    lsp/messages/window/ShowMessageNotification.cpp lsp/messages/window/ShowMessageNotification.h
    lsp/messages/window/ShowMessageRequest.cpp lsp/messages/window/ShowMessageRequest.h
    lsp/messages/window/ShowMessageResponse.cpp lsp/messages/window/ShowMessageResponse.h

    # LSP Factory
    lsp/factory/LspMessageFactory.cpp lsp/factory/LspMessageFactory.h
    lsp/factory/LspMessageRegistry.cpp lsp/factory/LspMessageRegistry.h

    # LSP Client
    lsp/client/LspClient.cpp lsp/client/LspClient.h

    # LSP Types
    lsp/types/LspTypes.h
    lsp/types/Position.cpp lsp/types/Position.h
    lsp/types/Range.cpp lsp/types/Range.h
    lsp/types/Location.cpp lsp/types/Location.h
    lsp/types/LocationLink.cpp lsp/types/LocationLink.h
    lsp/types/TextEdit.cpp lsp/types/TextEdit.h
    lsp/types/TextDocumentIdentifier.cpp lsp/types/TextDocumentIdentifier.h
    lsp/types/VersionedTextDocumentIdentifier.cpp lsp/types/VersionedTextDocumentIdentifier.h
    lsp/types/TextDocumentItem.cpp lsp/types/TextDocumentItem.h
    lsp/types/TextDocumentPositionParams.cpp lsp/types/TextDocumentPositionParams.h
    lsp/types/TextDocumentContentChangeEvent.cpp lsp/types/TextDocumentContentChangeEvent.h
    lsp/types/Diagnostic.cpp lsp/types/Diagnostic.h
    lsp/types/Command.cpp lsp/types/Command.h
    lsp/types/WorkspaceEdit.cpp lsp/types/WorkspaceEdit.h
    lsp/types/completion/CompletionItemKind.h
    lsp/types/completion/CompletionItem.cpp lsp/types/completion/CompletionItem.h
    lsp/types/completion/CompletionList.cpp lsp/types/completion/CompletionList.h
    lsp/types/completion/CompletionContext.cpp lsp/types/completion/CompletionContext.h
    lsp/types/capabilities/ClientCapabilities.cpp lsp/types/capabilities/ClientCapabilities.h
    lsp/types/capabilities/ServerCapabilities.cpp lsp/types/capabilities/ServerCapabilities.h
    lsp/types/capabilities/ServerInfo.cpp lsp/types/capabilities/ServerInfo.h
    lsp/types/capabilities/ClientInfo.cpp lsp/types/capabilities/ClientInfo.h

    # Language feature types
    lsp/types/SymbolKind.cpp lsp/types/SymbolKind.h
    lsp/types/DocumentSymbol.cpp lsp/types/DocumentSymbol.h
    lsp/types/MarkupContent.cpp lsp/types/MarkupContent.h
    lsp/types/Hover.cpp lsp/types/Hover.h
    lsp/types/CodeAction.cpp lsp/types/CodeAction.h
    lsp/types/CodeActionParams.cpp lsp/types/CodeActionParams.h
    lsp/types/DocumentHighlight.cpp lsp/types/DocumentHighlight.h
    lsp/types/SignatureHelp.cpp lsp/types/SignatureHelp.h
    lsp/types/ReferenceParams.cpp lsp/types/ReferenceParams.h
    lsp/types/RenameParams.cpp lsp/types/RenameParams.h
    lsp/types/SemanticTokens.cpp lsp/types/SemanticTokens.h
    lsp/types/PublishDiagnosticsParams.cpp lsp/types/PublishDiagnosticsParams.h
    lsp/types/WorkspaceSymbolParams.cpp lsp/types/WorkspaceSymbolParams.h


)

if (APPLE)
    set_target_properties(${CMAKE_PROJECT_NAME}
        PROPERTIES
            MACOSX_BUNDLE_BUNDLE_NAME "${CMAKE_PROJECT_NAME}"
            MACOSX_BUNDLE_GUI_IDENTIFIER "com.samRansbotham"
            MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
            MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION}
            RESOURCE "${TRYSAIL_RESOURCES}"
        )
elseif(WIN32)
        # Use Qt6 macro until CMake provides something
        # https://bugreports.qt.io/browse/QTBUG-87618
        set_target_properties( TrySail PROPERTIES
                OUTPUT_NAME TrySail
                WIN32_EXECUTABLE TRUE
                QT_TARGET_VERSION "${PROJECT_VERSION}"
                QT_TARGET_COMPANY_NAME "MainSail"
                QT_TARGET_DESCRIPTION "TrySail"
                QT_TARGET_COPYRIGHT "2024-2025 MainSail"
                QT_TARGET_PRODUCT_NAME "TrySail"
                QT_TARGET_RC_ICONS ${CMAKE_SOURCE_DIR}/Resources/TrySail.ico
        )
        _qt_internal_generate_win32_rc_file(TrySail)
endif()

add_subdirectory(pybind11)
target_include_directories(TrySail PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/pybind11/include)

target_include_directories(TrySail PRIVATE
    scintilla/include
    scintilla/src
    scintilla/qt/ScintillaEdit
    scintilla/qt/ScintillaEditBase
    lexilla/include
    lexilla/lexlib
)

target_compile_definitions(TrySail PRIVATE
    APP_VERSION=\"${PROJECT_VERSION}\"
    QT_DEPRECATED_WARNINGS
    QT_ENABLE_STRICT_MODE_UP_TO=0x060900
    QT_STRICT_ITERATORS
    QT_USE_QSTRINGBUILDER
)

target_link_libraries(TrySail PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Network
    Qt6::PrintSupport
    Qt6::Sql
    Qt6::Svg
    Qt6::Widgets
)

if(internal_LINK_NUITKA_EMBED)
    message("Linking to Nuitka-Python embed library.")

    target_compile_definitions(TrySail PRIVATE
        QT_NO_DEBUG_OUTPUT
        PYTHON_STATIC_BUILD
    )

    if(APPLE)

        # --- Stage 1: Combine all .a files per architecture using libtool ---
        set(UNIVERSAL_LIB_DIR ${CMAKE_CURRENT_BINARY_DIR}/nuitka_universal_libs)
        file(MAKE_DIRECTORY ${UNIVERSAL_LIB_DIR})

        # Glob all Nuitka libraries for each architecture
        file(GLOB ARM64_ALL_LIBS
                "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/arm64/PackagesFull.a"
                "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/arm64/libs/*.a"
        )
        file(GLOB X86_64_ALL_LIBS
                "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/x86_64/PackagesFull.a"
                "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/x86_64/libs/*.a"
        )

        # Define paths for the combined and final universal libraries
        set(COMBINED_ARM64_LIB ${UNIVERSAL_LIB_DIR}/libNuitkaCombined-arm64.a)
        set(COMBINED_X86_64_LIB ${UNIVERSAL_LIB_DIR}/libNuitkaCombined-x86_64.a)
        set(FINAL_UNIVERSAL_LIB ${UNIVERSAL_LIB_DIR}/libNuitkaCombined-universal.a)

        # Custom command to combine all ARM64 libraries into one
        add_custom_command(
                OUTPUT ${COMBINED_ARM64_LIB}
                COMMAND libtool -static -no_warning_for_no_symbols -o ${COMBINED_ARM64_LIB} ${ARM64_ALL_LIBS} 2> /dev/null
                DEPENDS ${ARM64_ALL_LIBS}
                COMMENT "Combining all ARM64 Nuitka libraries"
        )

        # Custom command to combine all x86_64 libraries into one
        add_custom_command(
                OUTPUT ${COMBINED_X86_64_LIB}
                COMMAND libtool -static -no_warning_for_no_symbols -o ${COMBINED_X86_64_LIB} ${X86_64_ALL_LIBS} 2> /dev/null
                DEPENDS ${X86_64_ALL_LIBS}
                COMMENT "Combining all x86_64 Nuitka libraries"
        )

        # --- Stage 2: Create the final universal library using lipo ---
        add_custom_command(
                OUTPUT ${FINAL_UNIVERSAL_LIB}
                COMMAND lipo -create -output ${FINAL_UNIVERSAL_LIB} ${COMBINED_ARM64_LIB} ${COMBINED_X86_64_LIB}
                DEPENDS ${COMBINED_ARM64_LIB} ${COMBINED_X86_64_LIB}
                COMMENT "Creating final universal Nuitka library"
        )

        # --- Drive the build process and link the final library ---
        add_custom_target(CreateNuitkaUniversalLib DEPENDS ${FINAL_UNIVERSAL_LIB})
        add_dependencies(TrySail CreateNuitkaUniversalLib)

        target_include_directories(TrySail PRIVATE
                ${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/arm64/include
        )

        # Link the single, final universal library
        target_link_libraries(TrySail PRIVATE
                ${FINAL_UNIVERSAL_LIB}
        )

        target_link_options(TrySail PRIVATE -flto=thin -Wl,-mllvm,-threads=0)

    elseif(WIN32)

        target_include_directories(TrySail PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/include
        )

        file(GLOB NuitkaPythonLibs
                "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/PackagesFull*.lib"
        )

        target_link_libraries(TrySail PRIVATE
                ${NuitkaPythonLibs}
        )

        file(GLOB NuitkaPythonDepLibs
                "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/libs/*.lib"
        )

        list(FILTER NuitkaPythonDepLibs EXCLUDE REGEX ".*sqlite3\\.lib$")
        list(FILTER NuitkaPythonDepLibs EXCLUDE REGEX ".*tiff\\.lib$")

        target_link_libraries(TrySail PRIVATE
                ${NuitkaPythonDepLibs}
        )

        target_link_libraries(TrySail PRIVATE shlwapi.lib Netapi32.lib shell32.lib winmm.lib msi.lib psapi.lib pdh.lib uuid.lib version.lib winspool.lib PowrProf.lib Rpcrt4.lib)

        target_link_directories(TrySail PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/../TrySailEmbeddedPython_ci/libs")

        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /FORCE /LTCG /NODEFAULTLIB:python3.lib /NODEFAULTLIB:python311.lib")
    endif()
else()
    message("Linking to system Python library.")

    find_package(Python3 3.11 EXACT COMPONENTS Development REQUIRED)
    include_directories(${Python3_INCLUDE_DIRS})
    link_directories(${Python3_RUNTIME_LIBRARY_DIRS})
    target_link_libraries(TrySail PRIVATE ${Python3_LIBRARIES} )
endif()

if(WIN32)
    target_compile_definitions(TrySail PRIVATE
        WINVER=_WIN32_WINNT_WIN6
        _WIN32_WINNT=_WIN32_WINNT_WIN6
        EXPORT_IMPORT_API=
    )

    target_compile_options(TrySail PRIVATE
        /utf-8
    )
endif()
