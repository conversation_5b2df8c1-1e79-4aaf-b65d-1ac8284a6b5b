#pragma once

#include "ScintillaEdit.h"
#include "TrySailGlobal.h"
#include "lsp/client/LspClient.h"

#include <QCborMap>
#include <memory>

class QCodeEditor : public ScintillaEdit
{
    Q_OBJECT
public:
    const enumCodeType           m_codeType;

public:
    explicit QCodeEditor( const enumCodeType codeType, QWidget* parent );

public:
    void setLanguageParsingColoring( const enumCodeType codeType, const Qt::ColorScheme colorScheme );
    void addSourceText( const enumLineType lineType, const QString& sLine );
    void autoComplete();
    void setFilePath( const QString& filePath );
    void initializeLsp( const QString& workspaceRoot );

    const char* m_lstKeywords{ nullptr };
    QCborMap    m_memory;
    QStringList m_listFunctions;

    enumLineType getLineType( const sptr_t line ) const;
    void         toggleLineType( const sptr_t line, const bool bWithUndo );
    void         setLineType( const sptr_t line, const enumLineType lineType, const bool bWithUndo );

    bool memoryVisible() const;
    void toggleMemoryVisible();
    void setMemoryVisible( const bool bVisible );

    sptr_t currentLine() const;

    std::tuple< sptr_t, sptr_t> findChunkFromLine( const sptr_t lineFocal ) const;
    sptr_t findStartFromLine( const sptr_t lineFocal ) const;
    sptr_t findEndFromLine(   const sptr_t lineFocal ) const;

    void setLexer( const enumCodeType typeCode );
    void setStyleColorFont( const sptr_t style, const int colorBack, const int colorFore );
    void setAnnotation( const int executionHandle, const QString sAnnotation, const bool bError );

    bool isFocal() const;

    QString getLines( const sptr_t lineStart, const sptr_t lineEnd ) const;
    int getExecutionHandleFromLine( const sptr_t nLine );
    sptr_t getLineFromExecutionHandle( const int executionHandle );
    void setExecutionStatus( const int executionHandle, const enumExecutionStatus statusExecutation );
    void resetAllPendingExecutionStatus();

public Q_SLOTS:
    void onEditCut();
    void onEditCopy();
    void onEditPaste();
    void onEditSelectAll();
    void onEditToggleCellType();
    void onZoomActual();
    void onZoomIn();
    void onZoomOut();
    void onLinesAdded( Scintilla::Position linesAdded );
    void onCharAdded( int ch );
    void onColorSchemeChanged( Qt::ColorScheme colorScheme );
    void onProgressMemoryUpdate( const QCborMap& cborMemory, const QStringList listFunctions );
    void onNotify( Scintilla::NotificationData* pscn );
    void showContextMenu( const QPoint& pos );

    void onDwellStart(int x, int y);
    void onDwellEnd(int x, int y);

    // LSP-related slots
    void onLspReady();
    void onLspCompletionReceived( int requestId, const QList<Lsp::CompletionItem>& items );
    void onLspCompletionItemResolved( int requestId, const Lsp::CompletionItem& resolvedItem );
    void onLspHoverReceived( int requestId, const Lsp::Hover& hover );
    void onLspError( const QString& message );
    void onAutoCompleteSelectionChange( Scintilla::Position position, const QString& text );
    void onAutoCompleteSelection( Scintilla::Position position, const QString& text );

Q_SIGNALS:
    void reportHelp(const QString& sContents );

private:
    void autoCompleteLsp();
    void autoCompleteTraditional();
    void updateLspDocument();

    std::unique_ptr<LspClient> m_lspClient;
    QString m_filePath;
    QString m_lastContent;
    int m_pendingCompletionRequestId;
    QList<Lsp::CompletionItem> m_currentLspCompletions; // Store current LSP completions for insertText lookup
    int m_pendingResolveRequestId; // Track pending completion item resolve requests
    int m_pendingHoverRequestId; // Track pending hover requests
};
