#include "ShowMessageResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue ShowMessageResponse::getResult() const {
    if (m_result.has_value()) {
        return m_result.value().toJson();
    }
    return QJsonValue::Null;
}

std::unique_ptr<ShowMessageResponse> ShowMessageResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    std::optional<MessageActionItem> result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        result = MessageActionItem::fromJson(json["result"].toObject());
    }
    
    return std::make_unique<ShowMessageResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ShowMessageResponse)
