#ifndef LSP_MESSAGES_WINDOW_SHOWMESSAGENOTIFICATION_H
#define LSP_MESSAGES_WINDOW_SHOWMESSAGENOTIFICATION_H

#include "../base/LspNotification.h"
#include "../base/LspError.h"
#include "../../types/LspTypes.h"
#include <memory>

namespace Lsp {

/**
 * The parameters of a notification message.
 */
struct ShowMessageParams {
    /**
     * The message type. See {@link MessageType}
     */
    integer type;

    /**
     * The actual message
     */
    QString message;

    QJsonObject toJson() const;
    static ShowMessageParams fromJson(const QJsonObject& json);
};

/**
 * The show message notification is sent from a server to a client to ask
 * the client to display a particular message in the user interface.
 */
class ShowMessageNotification : public LspNotification {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "window/showMessage")
    Q_CLASSINFO("LSP_TYPE", "notification")

public:
    ShowMessageNotification(const ShowMessageParams& params, QObject* parent = nullptr)
        : LspNotification("window/showMessage", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    ShowMessageParams getShowMessageParams() const { return m_params; }

    static std::unique_ptr<ShowMessageNotification> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    ShowMessageParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_WINDOW_SHOWMESSAGENOTIFICATION_H
