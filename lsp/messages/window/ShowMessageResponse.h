#ifndef LSP_MESSAGES_WINDOW_SHOWMESSAGERESPONSE_H
#define LSP_MESSAGES_WINDOW_SHOWMESSAGERESPONSE_H

#include "../base/LspResponse.h"
#include "ShowMessageRequest.h"
#include <QJsonObject>
#include <optional>
#include <memory>

namespace Lsp {

/**
 * Response to a window/showMessageRequest.
 * The result is MessageActionItem | null
 */
class ShowMessageResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "window/showMessageRequest")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    ShowMessageResponse(const QVariant& id, const std::optional<MessageActionItem>& result, QObject* parent = nullptr)
        : LspResponse(id, parent), m_result(result) {}

    QJsonValue getResult() const override;
    std::optional<MessageActionItem> getMessageActionItem() const { return m_result; }

    // Factory method
    static std::unique_ptr<ShowMessageResponse> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    std::optional<MessageActionItem> m_result;
};

} // namespace Lsp

#endif // LSP_MESSAGES_WINDOW_SHOWMESSAGERESPONSE_H
