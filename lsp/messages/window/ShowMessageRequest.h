#ifndef LSP_MESSAGES_WINDOW_SHOWMESSAGEREQUEST_H
#define LSP_MESSAGES_WINDOW_SHOWMESSAGEREQUEST_H

#include "../base/LspRequest.h"
#include "../base/LspError.h"
#include <QList>
#include <memory>

namespace Lsp {

/**
 * A message action item
 */
struct MessageActionItem {
    /**
     * A short title like 'Retry', 'Open Log' etc.
     */
    QString title;

    QJsonObject toJson() const;
    static MessageActionItem fromJson(const QJsonObject& json);
};

/**
 * The parameters of a show message request
 */
struct ShowMessageRequestParams {
    /**
     * The message type. See {@link MessageType}
     */
    integer type;

    /**
     * The actual message
     */
    QString message;

    /**
     * The message action items to present.
     */
    QList<MessageActionItem> actions;

    QJsonObject toJson() const;
    static ShowMessageRequestParams fromJson(const QJsonObject& json);
};

/**
 * The show message request is sent from a server to a client to ask the client
 * to display a particular message in the user interface. In addition to the show
 * message notification the request allows to pass actions and to wait for an
 * answer from the client.
 */
class ShowMessageRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "window/showMessageRequest")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    ShowMessageRequest() = default;
    ShowMessageRequest(const QVariant& id, const ShowMessageRequestParams& params);

    // Getters
    const ShowMessageRequestParams& params() const { return m_params; }

    // Setters
    void setParams(const ShowMessageRequestParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<ShowMessageRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    ShowMessageRequestParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_WINDOW_SHOWMESSAGEREQUEST_H
