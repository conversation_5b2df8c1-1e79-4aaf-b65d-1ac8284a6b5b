#include "ShowMessageNotification.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// ShowMessageParams implementation
QJsonObject ShowMessageParams::toJson() const {
    QJsonObject obj;
    obj["type"] = type;
    obj["message"] = message;
    return obj;
}

ShowMessageParams ShowMessageParams::fromJson(const QJsonObject& json) {
    ShowMessageParams params;
    params.type = json["type"].toInt();
    params.message = json["message"].toString();
    return params;
}

// ShowMessageNotification implementation
std::unique_ptr<ShowMessageNotification> ShowMessageNotification::fromJson(const QJsonObject& json) {
    ShowMessageParams params = ShowMessageParams::fromJson(json["params"].toObject());
    return std::make_unique<ShowMessageNotification>(params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ShowMessageNotification)
