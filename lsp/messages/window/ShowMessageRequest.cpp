#include "ShowMessageRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString ShowMessageRequest::METHOD = "window/showMessageRequest";

// MessageActionItem implementation
QJsonObject MessageActionItem::toJson() const {
    QJsonObject obj;
    obj["title"] = title;
    return obj;
}

MessageActionItem MessageActionItem::fromJson(const QJsonObject& json) {
    MessageActionItem item;
    item.title = json["title"].toString();
    return item;
}

// ShowMessageRequestParams implementation
QJsonObject ShowMessageRequestParams::toJson() const {
    QJsonObject obj;
    obj["type"] = type;
    obj["message"] = message;
    
    if (!actions.isEmpty()) {
        QJsonArray actionsArray;
        for (const auto& action : actions) {
            actionsArray.append(action.toJson());
        }
        obj["actions"] = actionsArray;
    }
    
    return obj;
}

ShowMessageRequestParams ShowMessageRequestParams::fromJson(const QJsonObject& json) {
    ShowMessageRequestParams params;
    params.type = json["type"].toInt();
    params.message = json["message"].toString();
    
    if (json.contains("actions")) {
        QJsonArray actionsArray = json["actions"].toArray();
        for (const auto& value : actionsArray) {
            params.actions.append(MessageActionItem::fromJson(value.toObject()));
        }
    }
    
    return params;
}

// ShowMessageRequest implementation
ShowMessageRequest::ShowMessageRequest(const QVariant& id, const ShowMessageRequestParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}

std::unique_ptr<ShowMessageRequest> ShowMessageRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    ShowMessageRequestParams params = ShowMessageRequestParams::fromJson(json["params"].toObject());
    return std::make_unique<ShowMessageRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ShowMessageRequest)
