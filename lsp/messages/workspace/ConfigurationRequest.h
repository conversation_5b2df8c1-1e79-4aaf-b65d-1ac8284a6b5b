#ifndef LSP_MESSAGES_WORKSPACE_CONFIGURATIONREQUEST_H
#define LSP_MESSAGES_WORKSPACE_CONFIGURATIONREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/LspTypes.h"
#include <QList>
#include <optional>
#include <memory>

namespace Lsp {

/**
 * A ConfigurationItem consists of the configuration section to ask for and an
 * additional scope URI. The configuration section asked for is defined by the
 * server and doesn't necessarily need to correspond to the configuration store
 * used by the client. So a server might ask for a configuration cpp.formatterOptions
 * but the client stores the configuration in an XML store layout differently.
 * It is up to the client to do the necessary conversion. If a scope URI is
 * provided the client should return the setting scoped to the provided resource.
 * If the client for example uses EditorConfig to manage its settings the
 * configuration should be returned for the passed resource URI. If the client
 * can't provide a configuration setting for a given scope then null needs to be
 * present in the returned array.
 */
struct ConfigurationItem {
    /**
     * The scope to get the configuration section for.
     */
    std::optional<QString> scopeUri;

    /**
     * The configuration section asked for.
     */
    std::optional<QString> section;

    QJsonObject toJson() const;
    static ConfigurationItem fromJson(const QJsonObject& json);
};

/**
 * The parameters of a configuration request.
 */
struct ConfigurationParams {
    QList<ConfigurationItem> items;

    QJsonObject toJson() const;
    static ConfigurationParams fromJson(const QJsonObject& json);
};

/**
 * The workspace/configuration request is sent from the server to the client to
 * fetch a certain configuration setting.
 *
 * This pull model replaces the old push model were the client signaled
 * configuration change via an event. If the server still needs to react to
 * configuration changes (since the server caches the result of
 * workspace/configuration requests) the server should register for an empty
 * configuration change event and empty the cache if such an event is received.
 */
class ConfigurationRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "workspace/configuration")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    ConfigurationRequest() = default;
    ConfigurationRequest(const QVariant& id, const ConfigurationParams& params);

    // Getters
    const ConfigurationParams& params() const { return m_params; }

    // Setters
    void setParams(const ConfigurationParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<ConfigurationRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    ConfigurationParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_WORKSPACE_CONFIGURATIONREQUEST_H
