#include "ConfigurationRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString ConfigurationRequest::METHOD = "workspace/configuration";

// ConfigurationItem implementation
QJsonObject ConfigurationItem::toJson() const {
    QJsonObject obj;
    if (scopeUri.has_value()) {
        obj["scopeUri"] = scopeUri.value();
    }
    if (section.has_value()) {
        obj["section"] = section.value();
    }
    return obj;
}

ConfigurationItem ConfigurationItem::fromJson(const QJsonObject& json) {
    ConfigurationItem item;
    if (json.contains("scopeUri")) {
        item.scopeUri = json["scopeUri"].toString();
    }
    if (json.contains("section")) {
        item.section = json["section"].toString();
    }
    return item;
}

// ConfigurationParams implementation
QJsonObject ConfigurationParams::toJson() const {
    QJsonObject obj;
    QJsonArray itemsArray;
    for (const auto& item : items) {
        itemsArray.append(item.toJson());
    }
    obj["items"] = itemsArray;
    return obj;
}

ConfigurationParams ConfigurationParams::fromJson(const QJsonObject& json) {
    ConfigurationParams params;
    QJsonArray itemsArray = json["items"].toArray();
    for (const auto& value : itemsArray) {
        params.items.append(ConfigurationItem::fromJson(value.toObject()));
    }
    return params;
}

// ConfigurationRequest implementation
ConfigurationRequest::ConfigurationRequest(const QVariant& id, const ConfigurationParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}

std::unique_ptr<ConfigurationRequest> ConfigurationRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    ConfigurationParams params = ConfigurationParams::fromJson(json["params"].toObject());
    return std::make_unique<ConfigurationRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ConfigurationRequest)
