#ifndef LSP_MESSAGES_LIFECYCLE_INITIALIZEREQUEST_H
#define LSP_MESSAGES_LIFECYCLE_INITIALIZEREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/LspTypes.h"
#include <QJsonArray>
#include <QJsonValue>
#include <optional>
#include <memory>

namespace Lsp {

/**
 * Initialize request parameters
 */
struct InitializeParams {
    std::optional<integer> processId;
    std::optional<ClientInfo> clientInfo;
    std::optional<QString> locale;
    std::optional<QString> rootPath; // Deprecated in favor of rootUri
    std::optional<DocumentUri> rootUri;
    std::optional<QJsonValue> initializationOptions;
    ClientCapabilities capabilities;
    std::optional<QString> trace;
    std::optional<QJsonArray> workspaceFolders;
    // Work done progress support (LSP 3.15+)
    std::optional<QString> workDoneToken;

    QJsonObject toJson() const;
    static InitializeParams fromJson(const QJsonObject& json);
};

/**
 * Initialize result
 */
struct InitializeResult {
    ServerCapabilities capabilities;
    std::optional<ServerInfo> serverInfo;
    // Position encoding support (LSP 3.17+)
    std::optional<QString> positionEncoding;

    QJsonObject toJson() const;
    static InitializeResult fromJson(const QJsonObject& json);
};

/**
 * Initialize request
 */
class InitializeRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "initialize")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    InitializeRequest(const QVariant& id, const InitializeParams& params, QObject* parent = nullptr)
        : LspRequest(id, "initialize", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    InitializeParams getInitializeParams() const { return m_params; }

    static std::unique_ptr<InitializeRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    InitializeParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LIFECYCLE_INITIALIZEREQUEST_H
