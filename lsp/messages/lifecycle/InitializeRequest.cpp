#include "InitializeRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// InitializeParams implementation
QJsonObject InitializeParams::toJson() const {
    QJsonObject obj;
    if (processId.has_value()) {
        obj["processId"] = processId.value();
    } else {
        obj["processId"] = QJsonValue::Null;
    }
    if (clientInfo.has_value()) {
        obj["clientInfo"] = clientInfo->toJson();
    }
    if (locale.has_value()) {
        obj["locale"] = locale.value();
    }
    if (rootPath.has_value()) {
        obj["rootPath"] = rootPath.value();
    }
    if (rootUri.has_value()) {
        obj["rootUri"] = rootUri.value();
    } else {
        obj["rootUri"] = QJsonValue::Null;
    }
    if (initializationOptions.has_value()) {
        obj["initializationOptions"] = initializationOptions.value();
    }
    obj["capabilities"] = capabilities.toJson();
    if (trace.has_value()) {
        obj["trace"] = trace.value();
    }
    if (workspaceFolders.has_value()) {
        obj["workspaceFolders"] = workspaceFolders.value();
    }
    if (workDoneToken.has_value()) {
        obj["workDoneToken"] = workDoneToken.value();
    }
    return obj;
}

InitializeParams InitializeParams::fromJson(const QJsonObject& json) {
    InitializeParams params;
    if (!json["processId"].isNull()) {
        params.processId = json["processId"].toInt();
    }
    if (json.contains("clientInfo")) {
        params.clientInfo = ClientInfo::fromJson(json["clientInfo"].toObject());
    }
    if (json.contains("locale")) {
        params.locale = json["locale"].toString();
    }
    if (json.contains("rootPath")) {
        params.rootPath = json["rootPath"].toString();
    }
    if (!json["rootUri"].isNull()) {
        params.rootUri = json["rootUri"].toString();
    }
    if (json.contains("initializationOptions")) {
        params.initializationOptions = json["initializationOptions"];
    }
    params.capabilities = ClientCapabilities::fromJson(json["capabilities"].toObject());
    if (json.contains("trace")) {
        params.trace = json["trace"].toString();
    }
    if (json.contains("workspaceFolders")) {
        params.workspaceFolders = json["workspaceFolders"].toArray();
    }
    if (json.contains("workDoneToken")) {
        params.workDoneToken = json["workDoneToken"].toString();
    }
    return params;
}

// InitializeResult implementation
QJsonObject InitializeResult::toJson() const {
    QJsonObject obj;
    obj["capabilities"] = capabilities.toJson();
    if (serverInfo.has_value()) {
        obj["serverInfo"] = serverInfo->toJson();
    }
    if (positionEncoding.has_value()) {
        obj["positionEncoding"] = positionEncoding.value();
    }
    return obj;
}

InitializeResult InitializeResult::fromJson(const QJsonObject& json) {
    InitializeResult result;
    result.capabilities = ServerCapabilities::fromJson(json["capabilities"].toObject());
    if (json.contains("serverInfo")) {
        result.serverInfo = ServerInfo::fromJson(json["serverInfo"].toObject());
    }
    if (json.contains("positionEncoding")) {
        result.positionEncoding = json["positionEncoding"].toString();
    }
    return result;
}

// InitializeRequest implementation
std::unique_ptr<InitializeRequest> InitializeRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"].toVariant();
    InitializeParams params = InitializeParams::fromJson(json["params"].toObject());
    return std::make_unique<InitializeRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(InitializeRequest)
