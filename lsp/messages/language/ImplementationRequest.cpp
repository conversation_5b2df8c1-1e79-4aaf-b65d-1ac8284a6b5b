#include "ImplementationRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString ImplementationRequest::METHOD = "textDocument/implementation";

ImplementationRequest::ImplementationRequest(const QVariant& id, const TextDocumentPositionParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}

std::unique_ptr<ImplementationRequest> ImplementationRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    TextDocumentPositionParams params = TextDocumentPositionParams::fromJson(json["params"].toObject());
    return std::make_unique<ImplementationRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ImplementationRequest)
