#include "ImplementationResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue ImplementationResponse::getResult() const {
    if (m_result.isEmpty()) {
        return QJsonValue::Null;
    }
    
    if (m_result.size() == 1) {
        return m_result.first().toJson();
    }
    
    QJsonArray array;
    for (const auto& location : m_result) {
        array.append(location.toJson());
    }
    return array;
}

std::unique_ptr<ImplementationResponse> ImplementationResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    QList<Location> result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        QJsonValue resultValue = json["result"];
        if (resultValue.isArray()) {
            QJsonArray array = resultValue.toArray();
            for (const auto& value : array) {
                result.append(Location::fromJson(value.toObject()));
            }
        } else if (resultValue.isObject()) {
            result.append(Location::fromJson(resultValue.toObject()));
        }
    }
    
    return std::make_unique<ImplementationResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ImplementationResponse)
