#ifndef LSP_MESSAGES_LANGUAGE_IMPLEMENTATIONRESPONSE_H
#define LSP_MESSAGES_LANGUAGE_IMPLEMENTATIONRESPONSE_H

#include "../base/LspResponse.h"
#include "../../types/LspTypes.h"
#include <QJsonObject>
#include <QList>
#include <memory>

namespace Lsp {

/**
 * Response to a textDocument/implementation request.
 * The result can be Location | Location[] | LocationLink[] | null
 */
class ImplementationResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/implementation")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    ImplementationResponse(const QVariant& id, const QList<Location>& result, QObject* parent = nullptr)
        : LspResponse(id, parent), m_result(result) {}

    QJsonValue getResult() const override;
    QList<Location> getImplementation() const { return m_result; }

    // Factory method
    static std::unique_ptr<ImplementationResponse> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    QList<Location> m_result;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_IMPLEMENTATIONRESPONSE_H
