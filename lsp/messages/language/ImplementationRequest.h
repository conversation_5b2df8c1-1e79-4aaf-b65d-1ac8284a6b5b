#ifndef LSP_MESSAGES_LANGUAGE_IMPLEMENTATIONREQUEST_H
#define LSP_MESSAGES_LANGUAGE_IMPLEMENTATIONREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/LspTypes.h"
#include <memory>

namespace Lsp {

/**
 * A request to resolve the implementation location of a symbol at a given text
 * document position. The request's parameter is of type [TextDocumentPositionParams]
 * (#TextDocumentPositionParams) the response is of type [Definition](#Definition) or a
 * Thenable that resolves to such.
 */
class ImplementationRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/implementation")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    ImplementationRequest() = default;
    ImplementationRequest(const QVariant& id, const TextDocumentPositionParams& params);

    // Getters
    const TextDocumentPositionParams& params() const { return m_params; }

    // Setters
    void setParams(const TextDocumentPositionParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<ImplementationRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    TextDocumentPositionParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_IMPLEMENTATIONREQUEST_H
