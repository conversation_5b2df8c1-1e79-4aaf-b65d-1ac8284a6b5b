#ifndef LSP_MESSAGES_DOCUMENT_WILLSAVENOTIFICATION_H
#define LSP_MESSAGES_DOCUMENT_WILLSAVENOTIFICATION_H

#include "../base/LspNotification.h"
#include "../../types/LspTypes.h"
#include <memory>

namespace Lsp {

/**
 * Represents reasons why a text document is saved.
 */
namespace TextDocumentSaveReason {
    constexpr integer Manual = 1;
    constexpr integer AfterDelay = 2;
    constexpr integer FocusOut = 3;
}

/**
 * The parameters send in a will save text document notification.
 */
struct WillSaveTextDocumentParams {
    /**
     * The document that will be saved.
     */
    TextDocumentIdentifier textDocument;

    /**
     * The 'TextDocumentSaveReason'.
     */
    integer reason;

    QJsonObject toJson() const;
    static WillSaveTextDocumentParams fromJson(const QJsonObject& json);
};

/**
 * The document will save notification is sent from the client to the server before
 * the document is actually saved.
 */
class WillSaveNotification : public LspNotification {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/willSave")
    Q_CLASSINFO("LSP_TYPE", "notification")

public:
    WillSaveNotification(const WillSaveTextDocumentParams& params, QObject* parent = nullptr)
        : LspNotification("textDocument/willSave", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    WillSaveTextDocumentParams getWillSaveParams() const { return m_params; }

    static std::unique_ptr<WillSaveNotification> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    WillSaveTextDocumentParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_DOCUMENT_WILLSAVENOTIFICATION_H
