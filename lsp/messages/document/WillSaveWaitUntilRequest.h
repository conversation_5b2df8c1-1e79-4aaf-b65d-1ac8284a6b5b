#ifndef LSP_MESSAGES_DOCUMENT_WILLSAVEWAITUNTILREQUEST_H
#define LSP_MESSAGES_DOCUMENT_WILLSAVEWAITUNTILREQUEST_H

#include "../base/LspRequest.h"
#include "WillSaveNotification.h"
#include <memory>

namespace Lsp {

/**
 * The document will save request is sent from the client to the server before
 * the document is actually saved. The request can return an array of TextEdits
 * which will be applied to the text document before it is saved. Please note that
 * clients might drop results if computing the text edits took too long or if a
 * server constantly fails on this request. This is done to keep the save fast and
 * reliable.
 */
class WillSaveWaitUntilRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/willSaveWaitUntil")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    WillSaveWaitUntilRequest() = default;
    WillSaveWaitUntilRequest(const QVariant& id, const WillSaveTextDocumentParams& params);

    // Getters
    const WillSaveTextDocumentParams& params() const { return m_params; }

    // Setters
    void setParams(const WillSaveTextDocumentParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<WillSaveWaitUntilRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    WillSaveTextDocumentParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_DOCUMENT_WILLSAVEWAITUNTILREQUEST_H
