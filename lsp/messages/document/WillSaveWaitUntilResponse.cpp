#include "WillSaveWaitUntilResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue WillSaveWaitUntilResponse::getResult() const {
    if (m_result.isEmpty()) {
        return QJsonValue::Null;
    }
    
    QJsonArray array;
    for (const auto& edit : m_result) {
        array.append(edit.toJson());
    }
    return array;
}

std::unique_ptr<WillSaveWaitUntilResponse> WillSaveWaitUntilResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    QList<TextEdit> result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        QJsonArray array = json["result"].toArray();
        for (const auto& value : array) {
            result.append(TextEdit::fromJson(value.toObject()));
        }
    }
    
    return std::make_unique<WillSaveWaitUntilResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(WillSaveWaitUntilResponse)
