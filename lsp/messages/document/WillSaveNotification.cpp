#include "WillSaveNotification.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// WillSaveTextDocumentParams implementation
QJsonObject WillSaveTextDocumentParams::toJson() const {
    QJsonObject obj;
    obj["textDocument"] = textDocument.toJson();
    obj["reason"] = reason;
    return obj;
}

WillSaveTextDocumentParams WillSaveTextDocumentParams::fromJson(const QJsonObject& json) {
    WillSaveTextDocumentParams params;
    params.textDocument = TextDocumentIdentifier::fromJson(json["textDocument"].toObject());
    params.reason = json["reason"].toInt();
    return params;
}

// WillSaveNotification implementation
std::unique_ptr<WillSaveNotification> WillSaveNotification::fromJson(const QJsonObject& json) {
    WillSaveTextDocumentParams params = WillSaveTextDocumentParams::fromJson(json["params"].toObject());
    return std::make_unique<WillSaveNotification>(params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(WillSaveNotification)
