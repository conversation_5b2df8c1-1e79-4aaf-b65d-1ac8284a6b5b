#ifndef LSP_MESSAGES_DOCUMENT_WILLSAVEWAITUNTILRESPONSE_H
#define LSP_MESSAGES_DOCUMENT_WILLSAVEWAITUNTILRESPONSE_H

#include "../base/LspResponse.h"
#include "../../types/LspTypes.h"
#include <QJsonObject>
#include <QList>
#include <memory>

namespace Lsp {

/**
 * Response to a textDocument/willSaveWaitUntil request.
 * The result is TextEdit[] | null
 */
class WillSaveWaitUntilResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/willSaveWaitUntil")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    WillSaveWaitUntilResponse(const QVariant& id, const QList<TextEdit>& result, QObject* parent = nullptr)
        : LspResponse(id, parent), m_result(result) {}

    QJsonValue getResult() const override;
    QList<TextEdit> getTextEdits() const { return m_result; }

    // Factory method
    static std::unique_ptr<WillSaveWaitUntilResponse> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    QList<TextEdit> m_result;
};

} // namespace Lsp

#endif // LSP_MESSAGES_DOCUMENT_WILLSAVEWAITUNTILRESPONSE_H
