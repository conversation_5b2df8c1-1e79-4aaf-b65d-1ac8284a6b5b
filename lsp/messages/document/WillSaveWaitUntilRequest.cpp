#include "WillSaveWaitUntilRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString WillSaveWaitUntilRequest::METHOD = "textDocument/willSaveWaitUntil";

WillSaveWaitUntilRequest::WillSaveWaitUntilRequest(const QVariant& id, const WillSaveTextDocumentParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}

std::unique_ptr<WillSaveWaitUntilRequest> WillSaveWaitUntilRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    WillSaveTextDocumentParams params = WillSaveTextDocumentParams::fromJson(json["params"].toObject());
    return std::make_unique<WillSaveWaitUntilRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(WillSaveWaitUntilRequest)
