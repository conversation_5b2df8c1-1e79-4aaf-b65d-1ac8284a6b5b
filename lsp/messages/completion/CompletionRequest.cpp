#include "CompletionRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// CompletionParams implementation
QJsonObject CompletionParams::toJson() const {
    QJsonObject obj;
    obj["textDocument"] = textDocument.toJson();
    obj["position"] = position.toJson();
    if (context.has_value()) {
        obj["context"] = context.value().toJson();
    }
    if (workDoneToken.has_value()) {
        obj["workDoneToken"] = workDoneToken.value();
    }
    if (partialResultToken.has_value()) {
        obj["partialResultToken"] = partialResultToken.value();
    }
    return obj;
}

CompletionParams CompletionParams::fromJson(const QJsonObject& json) {
    CompletionParams params;
    params.textDocument = TextDocumentIdentifier::fromJson(json["textDocument"].toObject());
    params.position = Position::fromJson(json["position"].toObject());
    if (json.contains("context")) {
        params.context = CompletionContext::fromJson(json["context"].toObject());
    }
    if (json.contains("workDoneToken")) {
        params.workDoneToken = json["workDoneToken"].toString();
    }
    if (json.contains("partialResultToken")) {
        params.partialResultToken = json["partialResultToken"].toString();
    }
    return params;
}

// CompletionRequest implementation
std::unique_ptr<CompletionRequest> CompletionRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"].toVariant();
    CompletionParams params = CompletionParams::fromJson(json["params"].toObject());
    return std::make_unique<CompletionRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(CompletionRequest)
