#ifndef LSP_MESSAGES_COMPLETION_COMPLETIONREQUEST_H
#define LSP_MESSAGES_COMPLETION_COMPLETIONREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/LspTypes.h"
#include "../../types/completion/CompletionContext.h"
#include <optional>
#include <memory>

namespace Lsp {

/**
 * Completion request parameters
 */
struct CompletionParams {
    TextDocumentIdentifier textDocument;
    Position position;
    std::optional<CompletionContext> context;
    // Work done progress support (LSP 3.15+)
    std::optional<QString> workDoneToken;
    // Partial result support (LSP 3.15+)
    std::optional<QString> partialResultToken;

    QJsonObject toJson() const;
    static CompletionParams fromJson(const QJsonObject& json);
};

/**
 * Completion request
 */
class CompletionRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/completion")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    CompletionRequest(const QVariant& id, const CompletionParams& params, QObject* parent = nullptr)
        : LspRequest(id, "textDocument/completion", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    CompletionParams getCompletionParams() const { return m_params; }

    static std::unique_ptr<CompletionRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    CompletionParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_COMPLETION_COMPLETIONREQUEST_H
