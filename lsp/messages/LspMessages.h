#ifndef LSP_MESSAGES_LSPMESSAGES_H
#define LSP_MESSAGES_LSPMESSAGES_H

// Base message classes
#include "base/LspMessage.h"
#include "base/LspRequest.h"
#include "base/LspResponse.h"
#include "base/LspNotification.h"
#include "base/LspError.h"
#include "base/GenericMessages.h"

// Lifecycle messages
#include "lifecycle/InitializeRequest.h"
#include "lifecycle/InitializeResponse.h"
#include "lifecycle/InitializedNotification.h"
#include "lifecycle/ShutdownRequest.h"
#include "lifecycle/ShutdownResponse.h"
#include "lifecycle/ExitNotification.h"

// Document synchronization messages
#include "document/DidOpenNotification.h"
#include "document/DidChangeNotification.h"
#include "document/DidSaveNotification.h"
#include "document/DidCloseNotification.h"
#include "document/WillSaveNotification.h"
#include "document/WillSaveWaitUntilRequest.h"
#include "document/WillSaveWaitUntilResponse.h"

// Completion messages
#include "completion/CompletionRequest.h"
#include "completion/CompletionResponse.h"

// Language feature messages
#include "language/HoverRequest.h"
#include "language/HoverResponse.h"
#include "language/DefinitionRequest.h"
#include "language/DefinitionResponse.h"
#include "language/DeclarationRequest.h"
#include "language/DeclarationResponse.h"
#include "language/TypeDefinitionRequest.h"
#include "language/TypeDefinitionResponse.h"
#include "language/ImplementationRequest.h"
#include "language/ImplementationResponse.h"
#include "language/DocumentSymbolRequest.h"
#include "language/DocumentHighlightRequest.h"
#include "language/DocumentHighlightResponse.h"
#include "language/ReferencesRequest.h"
#include "language/ReferencesResponse.h"
#include "language/RenameRequest.h"
#include "language/RenameResponse.h"
#include "language/CodeActionRequest.h"
#include "language/CodeActionResponse.h"
#include "language/SignatureHelpRequest.h"
#include "language/SignatureHelpResponse.h"
#include "language/SemanticTokensRequest.h"
#include "language/SemanticTokensResponse.h"
#include "language/CompletionItemResolveRequest.h"
#include "language/CompletionItemResolveResponse.h"
#include "language/PublishDiagnosticsNotification.h"

// Workspace messages
#include "workspace/WorkspaceSymbolRequest.h"
#include "workspace/WorkspaceSymbolResponse.h"
#include "workspace/ConfigurationRequest.h"

// Window messages
#include "window/ShowMessageNotification.h"
#include "window/ShowMessageRequest.h"
#include "window/ShowMessageResponse.h"

#endif // LSP_MESSAGES_LSPMESSAGES_H
