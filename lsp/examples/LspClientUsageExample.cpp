// Example demonstrating the new generic LspClient interface
// This file shows how to use LspClient for different language servers

#include "../client/LspClient.h"
#include <QApplication>

class LanguageServerExample : public QObject
{
    Q_OBJECT

public:
    LanguageServerExample(QObject* parent = nullptr) : QObject(parent) {}

    void demonstratePythonUsage()
    {
        // Method 1: Using the new generic interface
        auto pythonClient = std::make_unique<LspClient>(this);
        
        // Connect to generic signals
        connect(pythonClient.get(), &LspClient::languageServerReady, this, &LanguageServerExample::onLanguageServerReady);
        connect(pythonClient.get(), &LspClient::codeCompletionReceived, this, &LanguageServerExample::onCompletionReceived);
        connect(pythonClient.get(), &LspClient::codeHoverReceived, this, &LanguageServerExample::onHoverReceived);
        connect(pythonClient.get(), &LspClient::languageServerError, this, &LanguageServerExample::onLanguageServerError);

        // Initialize with Python configuration
        LanguageServerConfig pythonConfig = LspClient::createPythonConfig();
        pythonClient->initializeLanguageServer(pythonConfig, "/path/to/workspace");

        // Method 2: Using backward compatibility interface (deprecated but still works)
        auto legacyPythonClient = std::make_unique<LspClient>(this);
        
        // Connect to Python-specific signals (deprecated)
        connect(legacyPythonClient.get(), &LspClient::pythonLspReady, this, &LanguageServerExample::onPythonReady);
        connect(legacyPythonClient.get(), &LspClient::pythonCompletionReceived, this, &LanguageServerExample::onPythonCompletion);
        
        // Initialize using deprecated method
        legacyPythonClient->initializePythonLsp("/path/to/workspace");
    }

    void demonstrateCustomLanguageUsage()
    {
        // Example: Setting up a C++ language server (clangd)
        auto cppClient = std::make_unique<LspClient>(this);
        
        // Connect to generic signals
        connect(cppClient.get(), &LspClient::languageServerReady, this, &LanguageServerExample::onLanguageServerReady);
        connect(cppClient.get(), &LspClient::codeCompletionReceived, this, &LanguageServerExample::onCompletionReceived);
        
        // Create C++ configuration
        LanguageServerConfig cppConfig;
        cppConfig.languageId = "cpp";
        cppConfig.command = "clangd";
        cppConfig.arguments = {"--background-index", "--clang-tidy"};
        cppConfig.workingDirectory = "";
        
        // Set C++-specific initialization options
        QJsonObject initOptions;
        initOptions["compilationDatabasePath"] = "/path/to/compile_commands.json";
        cppConfig.initializationOptions = initOptions;
        
        // Initialize the C++ language server
        cppClient->initializeLanguageServer(cppConfig, "/path/to/cpp/workspace");
    }

    void demonstrateDocumentOperations()
    {
        // Assuming we have an initialized language server client
        if (m_lspClient && m_lspClient->isLanguageServerReady()) {
            
            // Open a document
            QString filePath = "/path/to/file.py";
            QString content = "def hello_world():\n    print('Hello, World!')";
            m_lspClient->openDocument(filePath, content);
            
            // Update document content
            QString newContent = "def hello_world():\n    print('Hello, World!')\n    return 42";
            m_lspClient->updateDocument(filePath, newContent);
            
            // Request completion at a specific position
            int requestId = m_lspClient->requestCodeCompletion(filePath, 1, 4); // line 1, character 4
            
            // Request hover information
            int hoverId = m_lspClient->requestCodeHover(filePath, 0, 4); // line 0, character 4
            
            // Close the document when done
            m_lspClient->closeDocument(filePath);
        }
    }

private slots:
    void onLanguageServerReady()
    {
        qDebug() << "Language server is ready!";
        // Now you can start opening documents and making requests
    }

    void onCompletionReceived(int requestId, const QList<Lsp::CompletionItem>& items)
    {
        qDebug() << "Received" << items.size() << "completion items for request" << requestId;
        for (const auto& item : items) {
            qDebug() << "  -" << item.label;
        }
    }

    void onHoverReceived(int requestId, const Lsp::Hover& hover)
    {
        qDebug() << "Received hover information for request" << requestId;
        if (hover.contents.has_value()) {
            // Process hover contents
        }
    }

    void onLanguageServerError(const QString& message)
    {
        qDebug() << "Language server error:" << message;
    }

    // Deprecated signal handlers (for backward compatibility example)
    void onPythonReady()
    {
        qDebug() << "Python LSP is ready! (deprecated signal)";
    }

    void onPythonCompletion(int requestId, const QList<Lsp::CompletionItem>& items)
    {
        qDebug() << "Python completion received (deprecated signal):" << items.size() << "items";
    }

private:
    std::unique_ptr<LspClient> m_lspClient;
};

#include "LspClientUsageExample.moc"
