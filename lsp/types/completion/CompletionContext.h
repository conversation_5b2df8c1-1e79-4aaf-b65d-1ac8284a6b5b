#ifndef LSP_TYPES_COMPLETION_COMPLETIONCONTEXT_H
#define LSP_TYPES_COMPLETION_COMPLETIONCONTEXT_H

#include "../LspTypesCommon.h"
#include <QJsonObject>
#include <QString>
#include <optional>

namespace Lsp {

/**
 * How a completion was triggered
 */
namespace CompletionTriggerKind {
    constexpr integer Invoked = 1;
    constexpr integer TriggerCharacter = 2;
    constexpr integer TriggerForIncompleteCompletions = 3;
}

/**
 * Contains additional information about the context in which a completion request is triggered.
 */
struct CompletionContext {
    /**
     * How the completion was triggered.
     */
    integer triggerKind;

    /**
     * The trigger character (a single character) that has trigger code complete.
     * Is undefined if `triggerKind !== CompletionTriggerKind.TriggerCharacter`
     */
    std::optional<QString> triggerCharacter;

    QJsonObject toJson() const;
    static CompletionContext fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_COMPLETION_COMPLETIONCONTEXT_H
