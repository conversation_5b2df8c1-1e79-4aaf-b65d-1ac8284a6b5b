#include "CompletionContext.h"

namespace Lsp {

QJsonObject CompletionContext::toJson() const {
    QJsonObject obj;
    obj["triggerKind"] = triggerKind;
    if (triggerCharacter.has_value()) {
        obj["triggerCharacter"] = triggerCharacter.value();
    }
    return obj;
}

CompletionContext CompletionContext::fromJson(const QJsonObject& json) {
    CompletionContext context;
    context.triggerKind = json["triggerKind"].toInt();
    if (json.contains("triggerCharacter")) {
        context.triggerCharacter = json["triggerCharacter"].toString();
    }
    return context;
}

} // namespace Lsp
