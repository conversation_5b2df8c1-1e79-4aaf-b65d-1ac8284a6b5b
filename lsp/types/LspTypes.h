#ifndef LSP_TYPES_LSPTYPES_H
#define LSP_TYPES_LSPTYPES_H

// Common types and includes
#include "LspTypesCommon.h"

// Basic types
#include "Position.h"
#include "Range.h"
#include "Location.h"
#include "LocationLink.h"
#include "TextEdit.h"

// Document types
#include "TextDocumentIdentifier.h"
#include "VersionedTextDocumentIdentifier.h"
#include "TextDocumentItem.h"
#include "TextDocumentPositionParams.h"
#include "TextDocumentContentChangeEvent.h"

// Diagnostic and command types
#include "Diagnostic.h"
#include "Command.h"
#include "WorkspaceEdit.h"

// Completion types
#include "completion/CompletionItemKind.h"
#include "completion/CompletionItem.h"
#include "completion/CompletionList.h"
#include "completion/CompletionContext.h"

// Capability types
#include "capabilities/ClientCapabilities.h"
#include "capabilities/ServerCapabilities.h"
#include "capabilities/ServerInfo.h"
#include "capabilities/ClientInfo.h"

// Language feature types
#include "SymbolKind.h"
#include "DocumentSymbol.h"
#include "MarkupContent.h"
#include "Hover.h"
#include "CodeAction.h"
#include "CodeActionParams.h"
#include "DocumentHighlight.h"
#include "SignatureHelp.h"
#include "ReferenceParams.h"
#include "RenameParams.h"
#include "SemanticTokens.h"
#include "PublishDiagnosticsParams.h"
#include "WorkspaceSymbolParams.h"

#endif // LSP_TYPES_LSPTYPES_H
