#include "LspClient.h"
#include <QJsonParseError>
#include <QDebug>
#include <QDir>
#include <QUrl>
#include <QStandardPaths>
#include <QMutexLocker>
#include <QCoreApplication>

LspClient::LspClient(QObject *parent)
  : QObject(parent), m_lspProcess(new QProcess(this)), m_messageId(0),
    m_lspInitialized(false), m_lspReady(false),
    m_initializationTimer(new QTimer(this)), m_nextVersion(1)
{
    // Setup LSP initialization timeout
    m_initializationTimer->setSingleShot(true);
    m_initializationTimer->setInterval(10000); // 10 seconds timeout
    connect(m_initializationTimer, &QTimer::timeout, this, &LspClient::onLanguageServerInitializationTimeout);
}

LspClient::~LspClient()
{
    shutdownLanguageServer();
}

void LspClient::startServer(const QString &command, const QStringList &arguments, const QString &workingDirectory)
{
    if (m_lspProcess->state() == QProcess::NotRunning) {
        // Connect QProcess signals to the appropriate slots
        connect(m_lspProcess, &QProcess::readyReadStandardOutput, this, &LspClient::onReadyReadStandardOutput);
        connect(m_lspProcess, &QProcess::readyReadStandardError, this, &LspClient::onReadyReadStandardError);
        connect(m_lspProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this, &LspClient::onProcessFinished);

        if (!workingDirectory.isEmpty()) {
            m_lspProcess->setWorkingDirectory(workingDirectory);
        }
        m_lspProcess->start(command, arguments);
        m_lspProcess->waitForStarted();

        if (m_lspProcess->state() == QProcess::Running) {
            emit serverStarted();
        } else {
            emit serverError(m_lspProcess->exitCode(), m_lspProcess->exitStatus());
        }
    }
}

void LspClient::stopServer()
{
    if (m_lspProcess->state() == QProcess::Running) {
        m_lspProcess->terminate();
        m_lspProcess->waitForFinished(3000); // Wait 3 seconds for graceful shutdown
        if (m_lspProcess->state() == QProcess::Running) {
            m_lspProcess->kill(); // Force kill if it doesn't terminate
        }
    }
}

int LspClient::sendRequest(const QString &method, const QJsonObject &params)
{
    QJsonObject request;
    request["jsonrpc"] = "2.0";
    request["id"] = ++m_messageId;
    request["method"] = method;
    request["params"] = params;

    sendMessage(request);
    return m_messageId;
}

void LspClient::sendNotification(const QString &method, const QJsonObject &params)
{
    QJsonObject notification;
    notification["jsonrpc"] = "2.0";
    notification["method"] = method;
    notification["params"] = params;

    sendMessage(notification);
}

QVariant LspClient::sendMessage(std::unique_ptr<Lsp::LspMessage> message)
{
    if (m_lspProcess->state() != QProcess::Running) {
        qWarning() << "LSP server is not running.";
        return QVariant();
    }

    QVariant messageId;

    // Track requests for response matching
    if (message->isRequest()) {
        auto request = dynamic_cast<Lsp::LspRequest*>(message.get());
        if (request) {
            messageId = request->getId();
            m_messageRegistry.registerPendingRequest(messageId, request->getMethod());
        }
    }

    // Serialize and send the message
    QByteArray messageBytes = Lsp::LspMessageFactory::serializeMessage(*message);
    m_lspProcess->write(messageBytes);

    return messageId;
}

int LspClient::initialize(const Lsp::InitializeParams& params)
{
    auto request = std::make_unique<Lsp::InitializeRequest>(++m_messageId, params);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

void LspClient::sendInitialized()
{
    auto notification = std::make_unique<Lsp::InitializedNotification>();
    sendMessage(std::move(notification));
}

int LspClient::shutdown()
{
    auto request = std::make_unique<Lsp::ShutdownRequest>(++m_messageId);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

void LspClient::sendExit()
{
    auto notification = std::make_unique<Lsp::ExitNotification>();
    sendMessage(std::move(notification));
}

void LspClient::didOpenTextDocument(const Lsp::TextDocumentItem& textDocument)
{
    Lsp::DidOpenNotification::DidOpenTextDocumentParams params;
    params.textDocument = textDocument;
    auto notification = std::make_unique<Lsp::DidOpenNotification>(params);
    sendMessage(std::move(notification));
}

void LspClient::didChangeTextDocument(const Lsp::VersionedTextDocumentIdentifier& textDocument,
                                     const QList<Lsp::TextDocumentContentChangeEvent>& contentChanges)
{
    Lsp::DidChangeNotification::DidChangeTextDocumentParams params;
    params.textDocument = textDocument;
    params.contentChanges = contentChanges;
    auto notification = std::make_unique<Lsp::DidChangeNotification>(params);
    sendMessage(std::move(notification));
}

void LspClient::didSaveTextDocument(const Lsp::TextDocumentIdentifier& textDocument,
                                   const std::optional<QString>& text)
{
    Lsp::DidSaveNotification::DidSaveTextDocumentParams params;
    params.textDocument = textDocument;
    params.text = text;
    auto notification = std::make_unique<Lsp::DidSaveNotification>(params);
    sendMessage(std::move(notification));
}

void LspClient::didCloseTextDocument(const Lsp::TextDocumentIdentifier& textDocument)
{
    Lsp::DidCloseNotification::DidCloseTextDocumentParams params;
    params.textDocument = textDocument;
    auto notification = std::make_unique<Lsp::DidCloseNotification>(params);
    sendMessage(std::move(notification));
}

int LspClient::requestCompletion(const Lsp::TextDocumentIdentifier& textDocument, const Lsp::Position& position)
{
    Lsp::CompletionParams params;
    params.textDocument = textDocument;
    params.position = position;
    auto request = std::make_unique<Lsp::CompletionRequest>(++m_messageId, params);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

int LspClient::requestCompletionItemResolve(const Lsp::CompletionItem& item)
{
    auto request = std::make_unique<Lsp::CompletionItemResolveRequest>(++m_messageId, item);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

int LspClient::requestHover(const Lsp::TextDocumentIdentifier& textDocument, const Lsp::Position& position)
{
    Lsp::TextDocumentPositionParams params;
    params.textDocument = textDocument;
    params.position = position;
    auto request = std::make_unique<Lsp::HoverRequest>(++m_messageId, params);
    QVariant id = sendMessage(std::move(request));
    return id.toInt();
}

void LspClient::sendMessage(const QJsonObject &message)
{
    if (m_lspProcess->state() != QProcess::Running) {
        qWarning() << "LSP server is not running.";
        return;
    }

    QJsonDocument doc(message);
    QByteArray messageBytes = doc.toJson(QJsonDocument::Compact);
    QByteArray header = "Content-Length: " + QByteArray::number(messageBytes.size()) + "\r\n\r\n";

    m_lspProcess->write(header + messageBytes);
}

void LspClient::onReadyReadStandardOutput()
{
    m_buffer.append(m_lspProcess->readAllStandardOutput());
    parseResponse(m_buffer);
}

void LspClient::onReadyReadStandardError()
{
    qWarning() << "LSP Server stderr:" << m_lspProcess->readAllStandardError();
}


void LspClient::parseResponse(const QByteArray &data)
{
    while (!m_buffer.isEmpty()) {
        int bytesConsumed = 0;
        auto message = m_messageFactory.parseMessage(m_buffer, bytesConsumed, &m_messageRegistry);

        if (!message) {
            // Not enough data for a complete message
            break;
        }

        // Remove consumed bytes from buffer
        m_buffer.remove(0, bytesConsumed);

        // Process the message
        processMessage(std::move(message));
    }
}

void LspClient::processMessage(std::unique_ptr<Lsp::LspMessage> message)
{
    // Get raw pointer for signal emission (keep ownership in this function)
    Lsp::LspMessage* rawMessage = message.get();
    emit messageReceived(rawMessage);

    if (message->isResponse()) {
        auto response = dynamic_cast<Lsp::LspResponse*>(rawMessage);
        if (response) {
            QVariant id = response->getId();
            QString method = m_messageRegistry.getPendingRequestMethod(id);
            m_messageRegistry.removePendingRequest(id);

            // Handle specific response types with proper type safety
            auto initResponse = dynamic_cast<Lsp::InitializeResponse*>(rawMessage);
            if (initResponse) {
                emit initialized(initResponse->getInitializeResult());
            }
            auto completionResponse = dynamic_cast<Lsp::CompletionResponse*>(rawMessage);
            if (completionResponse) {
                emit completionReceived(id.toInt(), completionResponse->getCompletionList());
            }
            auto resolveResponse = dynamic_cast<Lsp::CompletionItemResolveResponse*>(rawMessage);
            if (resolveResponse) {
                emit completionItemResolved(id.toInt(), resolveResponse->getCompletionItemResolve());
            }
            auto hoverResponse = dynamic_cast<Lsp::HoverResponse*>(rawMessage);
            if (hoverResponse) {
                emit hoverReceived(id.toInt(), hoverResponse->getHover());
            }

            // Emit generic response signal for backward compatibility
            QJsonObject result = response->getResult().toObject();
            emit responseReceived(id.toInt(), result);
        }
    } else if (message->isError()) {
        auto error = dynamic_cast<Lsp::LspError*>(rawMessage);
        if (error) {
            QVariant id = error->getId();
            m_messageRegistry.removePendingRequest(id);

            // Convert error to JSON for backward compatibility
            QJsonObject errorObj = error->getError().toJson();
            emit errorReceived(id.toInt(), errorObj);
        }
    } else if (message->isNotification()) {
        auto notification = dynamic_cast<Lsp::LspNotification*>(rawMessage);
        if (notification) {
            QString method = notification->getMethod();

            // Handle specific notification types
            if (method == "textDocument/didOpen") {
                auto didOpen = dynamic_cast<Lsp::DidOpenNotification*>(rawMessage);
                if (didOpen) {
                    emit textDocumentOpened(didOpen->getDidOpenParams());
                }
            } else if (method == "textDocument/didChange") {
                auto didChange = dynamic_cast<Lsp::DidChangeNotification*>(rawMessage);
                if (didChange) {
                    emit textDocumentChanged(didChange->getDidChangeParams());
                }
            } else if (method == "textDocument/didSave") {
                auto didSave = dynamic_cast<Lsp::DidSaveNotification*>(rawMessage);
                if (didSave) {
                    emit textDocumentSaved(didSave->getDidSaveParams());
                }
            } else if (method == "textDocument/didClose") {
                auto didClose = dynamic_cast<Lsp::DidCloseNotification*>(rawMessage);
                if (didClose) {
                    emit textDocumentClosed(didClose->getDidCloseParams());
                }
            }

            // Emit generic notification signal for backward compatibility
            QJsonObject params = notification->getParams();
            emit notificationReceived(method, params);
        }
    }
}


void LspClient::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    emit serverError(exitCode, exitStatus);
}

// Python LSP Manager implementation
bool LspClient::initializePythonLsp(const QString& workspaceRoot)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (m_pythonLspInitialized) {
        return true;
    }

    m_pythonWorkspaceRoot = workspaceRoot;

    // Connect signals for Python LSP
    connect(this, &LspClient::serverStarted, this, &LspClient::onPythonLspServerStarted);
    connect(this, &LspClient::initialized, this, &LspClient::onPythonLspInitialized);
    connect(this, &LspClient::completionReceived, this, &LspClient::onPythonLspCompletionReceived);
    connect(this, &LspClient::completionItemResolved, this, &LspClient::onPythonLspCompletionItemResolved);
    connect(this, &LspClient::hoverReceived, this, &LspClient::onPythonLspHoverReceived);
    connect(this, &LspClient::serverError, this, &LspClient::onPythonLspError);

    // Start the Python LSP server
    startPythonLspServer();

    return true;
}

void LspClient::shutdownPythonLsp()
{
    QMutexLocker locker(&m_pythonLspMutex);

    // Disconnect slots to avoid further processing.
    disconnect(m_lspProcess, &QProcess::readyReadStandardOutput, this, &LspClient::onReadyReadStandardOutput);
    disconnect(m_lspProcess, &QProcess::readyReadStandardError, this, &LspClient::onReadyReadStandardError);
    disconnect(m_lspProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this, &LspClient::onProcessFinished);

    if (m_pythonLspInitialized) {
        shutdown();
        sendExit();
    }
    stopServer();

    m_pythonLspInitialized = false;
    m_pythonLspReady = false;
    m_pythonInitializationTimer->stop();
}

bool LspClient::isPythonLspReady() const
{
    QMutexLocker locker(&m_pythonLspMutex);
    return m_pythonLspReady;
}

void LspClient::openPythonDocument(const QString& filePath, const QString& content)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (!m_pythonLspReady) {
        return;
    }

    QString uri = filePathToUri(filePath);
    int version = m_pythonNextVersion++;
    m_pythonDocumentVersions[filePath] = version;

    Lsp::TextDocumentItem textDoc;
    textDoc.uri = uri;
    textDoc.languageId = "python";
    textDoc.version = version;
    textDoc.text = content;

    didOpenTextDocument(textDoc);
}

void LspClient::updatePythonDocument(const QString& filePath, const QString& content, int version)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (!m_pythonLspReady) {
        return;
    }

    QString uri = filePathToUri(filePath);

    // Update version tracking
    if (version <= 0) {
        version = m_pythonNextVersion++;
    }
    m_pythonDocumentVersions[filePath] = version;

    Lsp::VersionedTextDocumentIdentifier versionedDoc;
    versionedDoc.uri = uri;
    versionedDoc.version = version;

    // Create a full document change event
    Lsp::TextDocumentContentChangeEvent changeEvent;
    changeEvent.text = content;
    // For full document updates, we don't set range

    QList<Lsp::TextDocumentContentChangeEvent> changes;
    changes.append(changeEvent);

    didChangeTextDocument(versionedDoc, changes);
}

void LspClient::closePythonDocument(const QString& filePath)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (!m_pythonLspReady) {
        return;
    }

    QString uri = filePathToUri(filePath);
    m_pythonDocumentVersions.remove(filePath);

    Lsp::TextDocumentIdentifier textDoc;
    textDoc.uri = uri;

    didCloseTextDocument(textDoc);
}

int LspClient::requestPythonCompletion(const QString& filePath, int line, int character)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (!m_pythonLspReady) {
        return -1;
    }

    QString uri = filePathToUri(filePath);

    Lsp::TextDocumentIdentifier textDoc;
    textDoc.uri = uri;

    Lsp::Position position;
    position.line = line;
    position.character = character;

    return requestCompletion(textDoc, position);
}

int LspClient::requestPythonCompletionItemResolve(const Lsp::CompletionItem& item)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (!m_pythonLspReady) {
        return -1;
    }

    return requestCompletionItemResolve(item);
}

int LspClient::requestPythonHover(const QString& filePath, int line, int character)
{
    QMutexLocker locker(&m_pythonLspMutex);

    if (!m_pythonLspReady) {
        return -1;
    }

    QString uri = filePathToUri(filePath);

    Lsp::TextDocumentIdentifier textDoc;
    textDoc.uri = uri;

    Lsp::Position position;
    position.line = line;
    position.character = character;

    return requestHover(textDoc, position);
}

// Python LSP Manager slot implementations
void LspClient::onPythonLspServerStarted()
{
    // Create initialization parameters
    Lsp::InitializeParams initParams;
    initParams.processId = QCoreApplication::applicationPid();

    // Set client info
    Lsp::ClientInfo clientInfo;
    clientInfo.name = "TrySail";
    clientInfo.version = "0.1.11";
    initParams.clientInfo = clientInfo;

    // Set root URI
    initParams.rootUri = filePathToUri(m_pythonWorkspaceRoot);

    // Set client capabilities for completion
    Lsp::ClientCapabilities capabilities;
    QJsonObject textDocCaps;

    // Completion capabilities
    QJsonObject completionCaps;
    completionCaps["dynamicRegistration"] = false;
    QJsonObject completionItem;
    completionItem["snippetSupport"] = false;
    completionItem["commitCharactersSupport"] = true;
    completionItem["documentationFormat"] = QJsonArray{"plaintext"};
    completionCaps["completionItem"] = completionItem;
    textDocCaps["completion"] = completionCaps;

    // Synchronization capabilities
    QJsonObject syncCaps;
    syncCaps["dynamicRegistration"] = false;
    syncCaps["willSave"] = false;
    syncCaps["willSaveWaitUntil"] = false;
    syncCaps["didSave"] = false;
    textDocCaps["synchronization"] = syncCaps;

    capabilities.textDocument = textDocCaps;
    initParams.capabilities = capabilities;

    QJsonObject initOptions;

    // Set completion options
    // QJsonObject completion;
    // completion["resolveEagerly"] = true;
    // initOptions["completion"] = completion;

    // Set jedi settings
    QJsonObject jediSettings;
    QJsonArray autoImportModules;
    autoImportModules.append("numpy");
    autoImportModules.append("scipy");
    autoImportModules.append("pandas");
    autoImportModules.append("matplotlib");
    jediSettings["autoImportModules"] = autoImportModules;
    initOptions["jediSettings"] = jediSettings;

    initParams.initializationOptions = initOptions;

    // Start initialization timeout
    m_pythonInitializationTimer->start();

    // Send initialize request
    int requestId = initialize(initParams);
}

void LspClient::onPythonLspInitialized(const Lsp::InitializeResult& result)
{
    m_pythonInitializationTimer->stop();

    // Send initialized notification
    sendInitialized();

    {
        QMutexLocker locker(&m_pythonLspMutex);
        m_pythonLspInitialized = true;
        m_pythonLspReady = true;
    }

    emit pythonLspReady();
}

void LspClient::onPythonLspCompletionReceived(int requestId, const Lsp::CompletionList& completionList)
{
    emit pythonCompletionReceived(requestId, completionList.items);
}

void LspClient::onPythonLspCompletionItemResolved(int requestId, const Lsp::CompletionItem& resolvedItem)
{
    emit pythonCompletionItemResolved(requestId, resolvedItem);
}

void LspClient::onPythonLspHoverReceived(int requestId, const Lsp::Hover& hover)
{
    emit pythonHoverReceived(requestId, hover);
}

void LspClient::onPythonLspError(int exitCode, QProcess::ExitStatus exitStatus)
{
    QString errorMsg = QString("PyLsp server error: exit code %1, status %2")
                       .arg(exitCode)
                       .arg(exitStatus == QProcess::NormalExit ? "Normal" : "Crash");

    qWarning() << errorMsg;

    {
        QMutexLocker locker(&m_pythonLspMutex);
        m_pythonLspInitialized = false;
        m_pythonLspReady = false;
    }

    emit pythonLspError(errorMsg);
}

void LspClient::onPythonLspInitializationTimeout()
{
    qWarning() << "PyLsp server initialization timeout";

    {
        QMutexLocker locker(&m_pythonLspMutex);
        m_pythonLspInitialized = false;
        m_pythonLspReady = false;
    }

    emit pythonLspError("LSP server initialization timeout");
}

// Python LSP Manager private method implementations
void LspClient::startPythonLspServer()
{
    startServer(qApp->applicationFilePath(), {"-c", "from jedi_language_server.cli import cli; cli()"});
}

QString LspClient::filePathToUri(const QString& filePath) const
{
    QUrl url = QUrl::fromLocalFile(QDir(filePath).absolutePath());
    return url.toString();
}

QString LspClient::uriToFilePath(const QString& uri) const
{
    QUrl url(uri);
    return url.toLocalFile();
}

QStringList LspClient::extractCompletionItems(const Lsp::CompletionList& completionList) const
{
    QStringList items;

    for (const Lsp::CompletionItem& item : completionList.items) {
        items.append(item.label);
    }

    return items;
}
