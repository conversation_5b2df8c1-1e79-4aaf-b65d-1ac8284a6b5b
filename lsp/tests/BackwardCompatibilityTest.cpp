// Test to verify backward compatibility of LspClient refactoring
// This test ensures that existing Python-specific code continues to work

#include "../client/LspClient.h"
#include <QObject>
#include <QTest>
#include <QSignalSpy>

class BackwardCompatibilityTest : public QObject
{
    Q_OBJECT

private slots:
    void testPythonLspBackwardCompatibility()
    {
        // Test that the old Python-specific interface still works
        LspClient client;
        
        // Test signal connections (should not crash)
        QSignalSpy pythonReadySpy(&client, &LspClient::pythonLspReady);
        QSignalSpy pythonCompletionSpy(&client, &LspClient::pythonCompletionReceived);
        QSignalSpy pythonErrorSpy(&client, &LspClient::pythonLspError);
        QSignalSpy pythonHoverSpy(&client, &LspClient::pythonHoverReceived);
        QSignalSpy pythonResolvedSpy(&client, &LspClient::pythonCompletionItemResolved);
        
        // Test that deprecated methods exist and can be called
        QVERIFY(client.metaObject()->indexOfMethod("initializePythonLsp(QString)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("shutdownPythonLsp()") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("isPythonLspReady()") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("openPythonDocument(QString,QString)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("updatePythonDocument(QString,QString,int)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("closePythonDocument(QString)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("requestPythonCompletion(QString,int,int)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("requestPythonHover(QString,int,int)") != -1);
        
        // Test that deprecated signals exist
        QVERIFY(client.metaObject()->indexOfSignal("pythonLspReady()") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("pythonCompletionReceived(int,QList<Lsp::CompletionItem>)") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("pythonLspError(QString)") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("pythonHoverReceived(int,Lsp::Hover)") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("pythonCompletionItemResolved(int,Lsp::CompletionItem)") != -1);
    }

    void testGenericInterfaceExists()
    {
        // Test that the new generic interface exists
        LspClient client;
        
        // Test signal connections for new interface
        QSignalSpy readySpy(&client, &LspClient::languageServerReady);
        QSignalSpy completionSpy(&client, &LspClient::codeCompletionReceived);
        QSignalSpy errorSpy(&client, &LspClient::languageServerError);
        QSignalSpy hoverSpy(&client, &LspClient::codeHoverReceived);
        QSignalSpy resolvedSpy(&client, &LspClient::codeCompletionItemResolved);
        
        // Test that new methods exist
        QVERIFY(client.metaObject()->indexOfMethod("initializeLanguageServer(LanguageServerConfig,QString)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("shutdownLanguageServer()") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("isLanguageServerReady()") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("openDocument(QString,QString)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("updateDocument(QString,QString,int)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("closeDocument(QString)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("requestCodeCompletion(QString,int,int)") != -1);
        QVERIFY(client.metaObject()->indexOfMethod("requestCodeHover(QString,int,int)") != -1);
        
        // Test that new signals exist
        QVERIFY(client.metaObject()->indexOfSignal("languageServerReady()") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("codeCompletionReceived(int,QList<Lsp::CompletionItem>)") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("languageServerError(QString)") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("codeHoverReceived(int,Lsp::Hover)") != -1);
        QVERIFY(client.metaObject()->indexOfSignal("codeCompletionItemResolved(int,Lsp::CompletionItem)") != -1);
    }

    void testPythonConfigHelper()
    {
        // Test the static helper method for Python configuration
        LanguageServerConfig config = LspClient::createPythonConfig();
        
        QCOMPARE(config.languageId, QString("python"));
        QVERIFY(!config.command.isEmpty());
        QVERIFY(!config.arguments.isEmpty());
        QVERIFY(config.initializationOptions.contains("jediSettings"));
    }

    void testMethodCompatibility()
    {
        // Test that calling deprecated methods doesn't crash
        LspClient client;
        
        // These should not crash (though they may not do anything useful without a server)
        QCOMPARE(client.isPythonLspReady(), false); // Should return false when not initialized
        
        // Test that the methods can be called (return values may vary)
        int result1 = client.requestPythonCompletion("/test/file.py", 0, 0);
        QCOMPARE(result1, -1); // Should return -1 when not ready
        
        int result2 = client.requestPythonHover("/test/file.py", 0, 0);
        QCOMPARE(result2, -1); // Should return -1 when not ready
    }
};

QTEST_MAIN(BackwardCompatibilityTest)
#include "BackwardCompatibilityTest.moc"
