#include "PythonIncludes.h"
#include "App.h"
#include "MainWindow.h"

#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QFontDatabase>
#include <QIcon>
#include <QString>
#include <QStringLiteral>
using namespace Qt::Literals::StringLiterals;

// global command line arguments
int    global_argc;
char** global_argv;


int main(int argc, char **argv)
{
    // save original command line arguments
    global_argc = argc;
    global_argv = argv;


    auto parser =  QCommandLineParser{};
    parser.setApplicationDescription("TrySail: a basic code editor edit/preview app");

    const auto optionCommand = QCommandLineOption( u"c"_s, u"Execute the Python code in 'command'. 'command' can be one or more statements separated by newlines, with significant leading whitespace as in normal module code."_s, u"command"_s );
    parser.addOption( optionCommand );

    const auto optionModule = QCommandLineOption( u"m"_s, u"Search sys.path for the named module and execute its contents as the __main__ module."_s, u"module-name"_s );
    parser.addOption( optionModule );

    const auto optionHelp = QCommandLineOption( {
                                            #ifdef Q_OS_WIN
                                            u"?"_s,
                                            #endif
                                            u"h"_s, u"help"_s }, u"Displays help on commandline options."_s );
    parser.addOption( optionHelp );

    const auto optionVersion = QCommandLineOption( { u"v"_s, u"version"_s }, u"Displays version information."_s );
    parser.addOption( optionVersion );


    auto listCommandLine = QStringList{};
    for ( auto i = 0; i < argc; ++i )
        listCommandLine.append( argv[i] );
    parser.parse( listCommandLine );

    if ( parser.isSet( optionHelp ) ) {

        const auto sHelp = parser.helpText();
        printf("%s\n", qPrintable(sHelp));
        return 0;

    } else if ( parser.isSet( optionVersion ) ) {

        const auto sVersion = u"TrySail %1"_s.arg( QStringLiteral( APP_VERSION ) );
        printf( "%s\n", qPrintable(sVersion) );
        return 0;

    } else if ( parser.isSet( optionCommand ) ) {

        #ifdef PYTHON_STATIC_BUILD
        Nuitka_Init(argc, argv);
        #else
        pybind11::scoped_interpreter guard;
        #endif

        // Execute the given Python code string.
        const auto sCode = parser.value( optionCommand );
        const auto stdCode = sCode.toStdString();
        pybind11::exec( stdCode );
        return 0;

    } else if ( parser.isSet( optionModule ) ) {

        #ifdef PYTHON_STATIC_BUILD
        Nuitka_Init(argc, argv);
        #else
        pybind11::scoped_interpreter guard;
        #endif

        // To make sure we properly replicate default module execution logic by directy using the
        // underlying runpy module.
        const auto sModule = parser.value( optionModule );
        const auto stdModule = sModule.toStdString();
        auto runpy = pybind11::module_::import("runpy");
        runpy.attr("run_module")(pybind11::str(stdModule), pybind11::arg("run_name") = "__main__");
        return 0;

    } else {

        QApplication::setStyle( u"fusion"_s );

        auto appGui = QApplication{ argc, argv };
        appGui.setOrganizationName( u"MainSail"_s );
        appGui.setApplicationName( u"TrySail"_s );
        appGui.setApplicationVersion( QStringLiteral( APP_VERSION ) );

        const auto icoMain = iconTrySail();
        appGui.setWindowIcon( *icoMain );

        QFontDatabase::addApplicationFont( u":/Resources/MonaspaceNeon-Regular.otf"_s );
        QFontDatabase::addApplicationFont( u":/Resources/ETBembo-RomanLF.otf"_s );

        auto win    = QTrySailMainWindow{};
        QObject::connect( &appGui, &QApplication::focusChanged, &win, &QTrySailMainWindow::onFocusChanged );
        win.showMaximized();

        const auto nReturn = appGui.exec();

        return nReturn;
    }
}
